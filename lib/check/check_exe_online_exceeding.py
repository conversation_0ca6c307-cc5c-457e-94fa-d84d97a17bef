# -*- coding: utf-8 -*-
"""
@File  : check_exe_online_exceeding.py
@Author: <PERSON>
@Date  : 2025/8/11 14:22
@Desc  : 执行报告超标排放量信息表中污染物排污浓度与在线监测数据中超标监测指标/超标时段/超标数据校验

功能说明：
- 校验在线监测企业废水废气的排口超标监测指标、超标时段、超标数据是否都在执行报告中有记录
- 废气校验小时浓度，废水校验日均值浓度
- 废气只校验氮氧化物、二氧化硫、非甲烷总烃、颗粒物（烟尘）4种污染物
- 废水只校验总磷、总氮、氨氮、化学需氧量4种污染物
- 只校验关联表中存在的企业的排口的污染物
- 所有涉及region_code字段的表都需要筛选出对应城市的记录

输入参数：
- city: 城市名
- start_date: 起始时间，精确到日，格式：'YYYY-MM-DD'
- end_date: 结束时间，精确到日，格式：'YYYY-MM-DD'

输出：
- 存在问题的记录数组，包含执行报告与在线监测各自的企业、排口、污染物表述和编码
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import time

from lib import logger
from lib.check.base import get_db_cursor, timing_decorator


# 配置参数
CONFIG = {
    'water_tolerance': 0.1,  # 废水浓度容差：正负10%
    'min_daily_records': 18,  # 废水日均值计算最小记录数
    'cache_timeout': 600,  # 缓存超时时间（秒）
    'batch_size': 1000  # 批处理大小
}

# 污染物配置
POLLUTANT_CONFIG = {
    'air': ['氮氧化物', '二氧化硫', '非甲烷总烃', '颗粒物', '烟尘'],  # 废气污染物
    'water': ['总磷', '总氮', '氨氮', '化学需氧量']  # 废水污染物
}

# 全局缓存
_CACHE = {
    'city_codes': {},
    'std_codes': {},
    'enterprise_mappings': {},
    'last_update': {}
}

# 性能统计
_PERFORMANCE_STATS = {
    'total_runs': 0,
    'last_run_time': 0.0,
    'total_anomalies': 0
}


def validate_input_params(city: str, start_date: str, end_date: str) -> bool:
    """
    验证输入参数的有效性

    Args:
        city (str): 城市名称
        start_date (str): 起始日期
        end_date (str): 结束日期

    Returns:
        bool: 参数是否有效
    """
    try:
        # 验证城市名称
        if not city or not isinstance(city, str) or city.strip() == '':
            logger.error("城市名称不能为空且必须是非空字符串")
            return False

        # 验证日期格式和有效性
        try:
            start_dt = datetime.strptime(start_date.strip(), '%Y-%m-%d')
            end_dt = datetime.strptime(end_date.strip(), '%Y-%m-%d')
        except ValueError as e:
            logger.error(f"日期格式错误，请使用 'YYYY-MM-DD' 格式: {str(e)}")
            return False

        # 验证日期逻辑
        if start_dt > end_dt:
            logger.error("起始日期不能晚于结束日期")
            return False

        # 验证日期范围
        days_diff = (end_dt - start_dt).days
        if days_diff < 0:
            logger.error("日期范围计算错误")
            return False

        return True

    except Exception as e:
        logger.error(f"参数验证过程中发生未预期错误: {str(e)}")
        return False


def get_city_code(city: str) -> str:
    """
    根据城市名称获取区域编码

    Args:
        city (str): 城市名称

    Returns:
        str: 城市编码

    Raises:
        ValueError: 城市名称无效或未找到对应编码
        Exception: 数据库查询失败
    """
    if not city or not isinstance(city, str):
        raise ValueError("城市名称不能为空且必须是字符串")

    city = city.strip()
    if not city:
        raise ValueError("城市名称不能为空字符串")

    # 检查缓存
    current_time = time.time()
    cache_key = f'city_code_{city}'

    if (cache_key in _CACHE['city_codes'] and
        cache_key in _CACHE['last_update'] and
        current_time - _CACHE['last_update'][cache_key] < CONFIG['cache_timeout']):
        logger.debug(f'从缓存获取城市{city}的区域编码')
        return _CACHE['city_codes'][cache_key]

    logger.info(f'查询城市{city}的区域编码...')

    db = None
    try:
        db = get_db_cursor('main')

        sql = "SELECT city_code FROM dim_ref_comm_prov_city WHERE city_name = '{}'".format(city)
        result = db.query_sql(sql)

        if result.empty:
            # 尝试模糊匹配
            sql_fuzzy = "SELECT city_code, value FROM dim_ref_comm_prov_city WHERE city_name LIKE '{}' LIMIT 5".format(f'%{city}%')
            fuzzy_result = db.query_sql(sql_fuzzy)

            if not fuzzy_result.empty:
                suggestions = ', '.join(fuzzy_result['value'].tolist())
                raise ValueError(f'未找到城市"{city}"的区域编码。可能的匹配: {suggestions}')
            else:
                raise ValueError(f'未找到城市"{city}"的区域编码，请检查城市名称是否正确')

        city_code = str(result.iloc[0]['city_code']).strip()
        if not city_code:
            raise ValueError(f'城市"{city}"的区域编码为空')

        # 更新缓存
        _CACHE['city_codes'][cache_key] = city_code
        _CACHE['last_update'][cache_key] = current_time

        logger.info(f'城市{city}的区域编码为: {city_code}')
        return city_code

    except Exception as e:
        logger.error(f'查询城市编码时发生错误: {str(e)}')
        raise
    finally:
        if db:
            try:
                db.close()
            except Exception as e:
                logger.warning(f'关闭数据库连接时发生错误: {str(e)}')


def get_pollutant_std_codes(pollutant_type: str) -> Dict[str, str]:
    """
    获取污染物标准因子编码映射

    Args:
        pollutant_type (str): 污染物类型，'a'(废气) 或 'w'(废水)

    Returns:
        Dict[str, str]: 污染物名称到标准编码的映射
    """
    # 检查缓存
    current_time = time.time()
    cache_key = f'std_codes_{pollutant_type}'

    if (cache_key in _CACHE['std_codes'] and
        cache_key in _CACHE['last_update'] and
        current_time - _CACHE['last_update'][cache_key] < CONFIG['cache_timeout']):
        logger.debug(f'从缓存获取{pollutant_type}类型污染物标准编码')
        return _CACHE['std_codes'][cache_key]

    logger.info(f'查询{pollutant_type}类型污染物标准因子编码...')

    db = get_db_cursor('main')

    # 根据污染物类型获取对应的污染物列表
    if pollutant_type == 'a':
        pollutants = POLLUTANT_CONFIG['air']
    elif pollutant_type == 'w':
        pollutants = POLLUTANT_CONFIG['water']
    else:
        raise ValueError(f'不支持的污染物类型: {pollutant_type}')

    # 构建查询条件
    pollutant_list = "', '".join(pollutants)
    sql = f"""
        SELECT monitoring_indicator, std_code, std_name
        FROM basic_information_std_factor_map
        WHERE std_type = '{pollutant_type}'
        AND monitoring_indicator IN ('{pollutant_list}')
    """

    result = db.query_sql(sql)
    db.close()

    if result.empty:
        logger.warning(f'未找到{pollutant_type}类型的污染物标准编码')
        return {}

    # 检查是否所有污染物都在列表中
    if not set(pollutants).issubset(result['monitoring_indicator']):
        logger.warning(f'未找到{pollutant_type}类型的污染物标准编码: {set(pollutants) - set(result["monitoring_indicator"])}')

    # 转换为字典
    std_codes_map = dict(zip(result['monitoring_indicator'], result['std_code']))

    # 更新缓存
    _CACHE['std_codes'][cache_key] = std_codes_map
    _CACHE['last_update'][cache_key] = current_time

    logger.info(f'获取到{len(std_codes_map)}个{pollutant_type}类型污染物标准编码')

    return std_codes_map


def get_enterprise_mapping(city_code: str) -> pd.DataFrame:
    """
    获取企业关联映射表数据

    Args:
        city_code (str): 城市编码

    Returns:
        pd.DataFrame: 企业关联映射数据
    """
    # 检查缓存
    current_time = time.time()
    cache_key = f'enterprise_mapping_{city_code}'

    if (cache_key in _CACHE['enterprise_mappings'] and
        cache_key in _CACHE['last_update'] and
        current_time - _CACHE['last_update'][cache_key] < CONFIG['cache_timeout']):
        logger.debug(f'从缓存获取区域{city_code}的企业关联映射数据')
        return _CACHE['enterprise_mappings'][cache_key]

    logger.info(f'获取区域{city_code}的企业关联映射数据...')

    db = get_db_cursor('main')

    sql = """
        SELECT f_enterprise_id, f_enterprise_name, outlet_id, std_code, outlet_name,
               online_enterprise_id, emission_id, pollution_name, region_code, excess_monitoring_type
        FROM zx_basic_map_zx_map_jx_over_std_chongqing
        WHERE region_code = '{city_code}'
    """.format(city_code=city_code)

    mapping_data = db.query_sql(sql)
    db.close()

    # 更新缓存
    _CACHE['enterprise_mappings'][cache_key] = mapping_data.copy()
    _CACHE['last_update'][cache_key] = current_time

    logger.info(f'获取到{len(mapping_data)}条企业关联映射数据')

    return mapping_data


def extract_online_air_exceeding_data(city_code: str, start_date: str, end_date: str,
                                    std_codes_map: Dict[str, str]) -> pd.DataFrame:
    """
    提取在线监测废气超标数据

    Args:
        city_code (str): 城市编码
        start_date (str): 起始日期
        end_date (str): 结束日期
        std_codes_map (Dict[str, str]): 标准因子编码映射

    Returns:
        pd.DataFrame: 废气超标数据
    """
    logger.info(f'提取区域{city_code}的在线监测废气超标数据...')

    if not std_codes_map:
        logger.warning('废气标准因子编码映射为空')
        return pd.DataFrame()

    db = get_db_cursor('main')

    # 构建污染物筛选条件
    pollutant_names = list(std_codes_map.keys())
    pollutant_list = "', '".join(pollutant_names)

    sql = f"""
        SELECT air.pollution_source_name, air.emission_id, air.emission_name,
               air.monitoring_time, air.pollution_code, air.pollution_name,
               air.standard_value, air.monitoring_value, air.mn,
               station.online_enterprise_id
        FROM data_emission_air_monitor_hour air
        JOIN data_emission_station station ON air.mn = station.mn
        WHERE air.monitoring_time >= '{start_date}'
        AND air.monitoring_time <= '{end_date} 23:59:59'
        AND air.flag_code IS NULL
        AND air.pollution_name IN ('{pollutant_list}')
        AND CAST(air.monitoring_value AS FLOAT) > CAST(air.standard_value AS FLOAT)
        ORDER BY air.monitoring_time, air.emission_id, air.pollution_name
    """

    result = db.query_sql(sql)
    db.close()

    logger.info(f'提取到{len(result)}条废气超标数据')

    return result


def extract_online_water_exceeding_data(city_code: str, start_date: str, end_date: str,
                                      std_codes_map: Dict[str, str]) -> pd.DataFrame:
    """
    提取在线监测废水超标数据并计算日均值

    Args:
        city_code (str): 城市编码
        start_date (str): 起始日期
        end_date (str): 结束日期
        std_codes_map (Dict[str, str]): 标准因子编码映射

    Returns:
        pd.DataFrame: 废水超标日均值数据
    """
    logger.info(f'提取区域{city_code}的在线监测废水超标数据...')

    if not std_codes_map:
        logger.warning('废水标准因子编码映射为空')
        return pd.DataFrame()

    db = get_db_cursor('main')

    # 构建污染物筛选条件
    pollutant_names = list(std_codes_map.keys())
    pollutant_list = "', '".join(pollutant_names)

    # 先获取所有有效的废水监测数据
    sql = f"""
        SELECT water.pollution_source_name, water.emission_id, water.emission_name,
               water.monitoring_time, water.pollution_code, water.pollution_name,
               water.standard_value, water.monitoring_value, water.mn,
               station.online_enterprise_id
        FROM data_emission_water_monitor_hour water
        JOIN data_emission_station station ON water.mn = station.mn
        WHERE water.monitoring_time >= '{start_date}'
        AND water.monitoring_time <= '{end_date} 23:59:59'
        AND water.flag_code IS NULL
        AND water.pollution_name IN ('{pollutant_list}')
        ORDER BY water.monitoring_time, water.emission_id, water.pollution_name
    """

    result = db.query_sql(sql)
    db.close()

    if result.empty:
        logger.info('未找到废水监测数据')
        return pd.DataFrame()

    logger.info(f'提取到{len(result)}条废水监测数据，开始计算日均值...')

    # 计算日均值
    daily_avg_data = calculate_water_daily_average(result)

    logger.info(f'计算得到{len(daily_avg_data)}条废水日均值数据')

    return daily_avg_data


def calculate_water_daily_average(water_data: pd.DataFrame) -> pd.DataFrame:
    """
    计算废水监测数据的日均值，只对日监测记录大于18条的计算日均值

    Args:
        water_data (pd.DataFrame): 原始废水监测数据

    Returns:
        pd.DataFrame: 日均值数据（包含超标记录）
    """
    if water_data is None or water_data.empty:
        logger.info('废水监测数据为空，跳过日均值计算')
        return pd.DataFrame()

    logger.info(f'开始计算废水日均值，原始数据: {len(water_data)} 条')

    try:
        # 确保数值列为float类型，增强错误处理
        water_data = water_data.copy()

        # 检查必需列是否存在
        required_columns = ['monitoring_value', 'standard_value', 'monitoring_time',
                          'online_enterprise_id', 'emission_id', 'pollution_name']
        missing_columns = [col for col in required_columns if col not in water_data.columns]
        if missing_columns:
            logger.error(f'废水数据缺少必需列: {missing_columns}')
            return pd.DataFrame()

        # 数据类型转换，增强错误处理
        water_data['monitoring_value'] = pd.to_numeric(water_data['monitoring_value'], errors='coerce')
        water_data['standard_value'] = pd.to_numeric(water_data['standard_value'], errors='coerce')

        # 检查转换后的空值
        null_monitoring = water_data['monitoring_value'].isnull().sum()
        null_standard = water_data['standard_value'].isnull().sum()
        if null_monitoring > 0:
            logger.warning(f'监测值转换失败的记录数: {null_monitoring}')
        if null_standard > 0:
            logger.warning(f'标准值转换失败的记录数: {null_standard}')

        # 移除无效数据
        water_data = water_data.dropna(subset=['monitoring_value', 'standard_value'])
        if water_data.empty:
            logger.warning('数据清洗后无有效记录')
            return pd.DataFrame()

        # 时间转换
        water_data['monitoring_time'] = pd.to_datetime(water_data['monitoring_time'], errors='coerce')
        water_data = water_data.dropna(subset=['monitoring_time'])

        if water_data.empty:
            logger.warning('时间转换后无有效记录')
            return pd.DataFrame()

    except Exception as e:
        logger.error(f'数据预处理失败: {str(e)}')
        return pd.DataFrame()

    # 提取日期
    water_data['monitoring_date'] = water_data['monitoring_time'].dt.date

    # 按企业、排口、污染物、日期分组
    group_keys = ['online_enterprise_id', 'emission_id', 'pollution_name', 'monitoring_date']

    daily_results = []
    total_groups = 0
    valid_groups = 0
    exceeding_groups = 0

    for name, group in water_data.groupby(group_keys):
        total_groups += 1

        # 检查当日监测记录数量
        if len(group) <= CONFIG['min_daily_records']:
            logger.debug(f'跳过记录数不足的组: {name}, 记录数: {len(group)}')
            continue

        valid_groups += 1

        # 计算日均值
        daily_avg_value = group['monitoring_value'].mean()
        daily_std_value = group['standard_value'].iloc[0]  # 标准值通常是固定的

        # 检查日均值是否超标
        if daily_avg_value > daily_std_value:
            exceeding_groups += 1
            daily_record = {
                'online_enterprise_id': name[0],
                'emission_id': name[1],
                'pollution_name': name[2],
                'monitoring_date': name[3],
                'daily_avg_value': daily_avg_value,
                'standard_value': daily_std_value,
                'daily_record_count': len(group),
                'pollution_source_name': group['pollution_source_name'].iloc[0],
                'emission_name': group['emission_name'].iloc[0],
                'pollution_code': group['pollution_code'].iloc[0],
                'mn': group['mn'].iloc[0]
            }
            daily_results.append(daily_record)

    logger.info(f'日均值计算完成: 总组数={total_groups}, 有效组数={valid_groups}, 超标组数={exceeding_groups}')

    if daily_results:
        return pd.DataFrame(daily_results)
    else:
        return pd.DataFrame()


def extract_exec_air_exceeding_data(city_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    提取执行报告废气超标数据

    Args:
        city_code (str): 城市编码
        start_date (str): 起始日期
        end_date (str): 结束日期

    Returns:
        pd.DataFrame: 执行报告废气超标数据
    """
    logger.info(f'提取区域{city_code}的执行报告废气超标数据...')

    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    gas_table = get_table_name("zxgl_basic_data_waste_gas_over_std_hour_quart_report", city_code)

    sql = f"""
        SELECT f_enterprise_id, f_enterprise_name, outlet_id, std_code,
               over_std_start_time, over_std_end_time, actual_emission_con,
               excess_monitoring_type, excess_reason_description, report_time
        FROM {gas_table}
        WHERE region_code = '{city_code}'
        AND over_std_start_time >= '{start_date}'
        AND over_std_end_time <= '{end_date} 23:59:59'
        ORDER BY over_std_start_time, f_enterprise_id, outlet_id
    """

    result = db.query_sql(sql)
    db.close()

    logger.info(f'提取到{len(result)}条执行报告废气超标数据')

    return result


def extract_exec_water_exceeding_data(city_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    提取执行报告废水超标数据

    Args:
        city_code (str): 城市编码
        start_date (str): 起始日期
        end_date (str): 结束日期

    Returns:
        pd.DataFrame: 执行报告废水超标数据
    """
    logger.info(f'提取区域{city_code}的执行报告废水超标数据...')

    db = get_db_cursor('main')

    # 动态生成表名
    from lib.check.table_name_generator import get_table_name
    water_table = get_table_name("zxgl_basic_data_waste_water_over_std_day_quart_report", city_code)

    sql = f"""
        SELECT f_enterprise_id, f_enterprise_name, outlet_id, std_code,
               over_std_start_time, over_std_end_time, actual_emission_con,
               excess_monitoring_type, excess_reason_description, report_time
        FROM {water_table}
        WHERE over_std_start_time >= '{start_date}'
        AND over_std_end_time <= '{end_date} 23:59:59'
        ORDER BY over_std_start_time, f_enterprise_id, outlet_id
    """

    result = db.query_sql(sql)
    db.close()

    logger.info(f'提取到{len(result)}条执行报告废水超标数据')

    return result


def check_time_overlap(online_time: datetime, exec_start_time: datetime, exec_end_time: datetime) -> bool:
    """
    检查在线监测时间是否在执行报告超标时间段内

    Args:
        online_time (datetime): 在线监测时间
        exec_start_time (datetime): 执行报告超标开始时间
        exec_end_time (datetime): 执行报告超标结束时间

    Returns:
        bool: 是否在时间段内
    """
    return exec_start_time <= online_time <= exec_end_time


def check_concentration_tolerance(online_value: float, exec_value: float, tolerance: float = None) -> bool:
    """
    检查浓度是否在容差范围内（用于废水日均值校验）

    Args:
        online_value (float): 在线监测值
        exec_value (float): 执行报告值
        tolerance (float): 容差比例，默认使用配置值

    Returns:
        bool: 是否在容差范围内
    """
    if tolerance is None:
        tolerance = CONFIG['water_tolerance']

    if exec_value == 0:
        return online_value == 0

    # 计算相对误差
    relative_error = abs(online_value - exec_value) / exec_value
    return relative_error <= tolerance


def transform2quarter(time: datetime | str) -> str:
    """
    将时间转换为季度字符串

    Args:
        time (datetime | str): 时间对象或字符串

    Returns:
        str: 季度字符串，格式为 "YYYY年第N季"
    """
    if isinstance(time, str):
        time = datetime.strptime(time, '%Y-%m-%d %H:%M:%S')

    year = time.year
    month = time.month
    quarter = (month - 1) // 3 + 1
    return f"{year}年第{quarter}季"


def validate_air_exceeding_records(online_air_data: pd.DataFrame, exec_air_data: pd.DataFrame,
                                 mapping_data: pd.DataFrame) -> List[Dict]:
    """
    校验废气超标记录

    Args:
        online_air_data (pd.DataFrame): 在线监测废气超标数据
        exec_air_data (pd.DataFrame): 执行报告废气超标数据
        mapping_data (pd.DataFrame): 企业关联映射数据

    Returns:
        List[Dict]: 问题记录列表，每个元素包含同一条在线监测记录的所有问题
    """
    logger.info('开始校验废气超标记录...')

    anomalies = []

    if online_air_data.empty:
        logger.info('无在线监测废气超标数据')
        return anomalies

    if exec_air_data.empty:
        logger.warning('无执行报告废气超标数据，所有在线监测超标记录都是问题')
        # 按在线监测记录分组处理
        online_groups = online_air_data.groupby(['online_enterprise_id', 'emission_id', 'monitoring_time'])

        for group_key, group_data in online_groups:
            online_enterprise_id, emission_id, monitoring_time = group_key

            # 创建问题记录
            anomaly_record = {
                'data_type': 'air',
                'online_enterprise_info': {
                    'online_enterprise_id': online_enterprise_id,
                    'pollution_source_name': group_data.iloc[0]['pollution_source_name'],
                    'emission_id': emission_id,
                    'emission_name': group_data.iloc[0]['emission_name']
                },
                'monitoring_time': monitoring_time,
                'report_time': transform2quarter(monitoring_time),
                'pollutant_issues': [],
                'has_issues': True
            }

            # 处理每个污染物
            for _, online_row in group_data.iterrows():
                mapping_match = mapping_data[
                    (mapping_data['online_enterprise_id'] == str(online_row['online_enterprise_id'])) &
                    (mapping_data['emission_id'] == str(online_row['emission_id'])) &
                    (mapping_data['pollution_name'] == online_row['pollution_name'])
                ]

                if not mapping_match.empty:
                    mapping_row = mapping_match.iloc[0]

                    # 设置执行报告企业信息
                    if 'exec_enterprise_info' not in anomaly_record:
                        anomaly_record['exec_enterprise_info'] = {
                            'f_enterprise_id': mapping_row['f_enterprise_id'],
                            'f_enterprise_name': mapping_row['f_enterprise_name'],
                            'outlet_id': mapping_row['outlet_id'],
                            'outlet_name': mapping_row['outlet_name']
                        }

                    pollutant_issue = {
                        'pollution_name': online_row['pollution_name'],
                        'pollution_code': online_row['pollution_code'],
                        'std_code': mapping_row['std_code'],
                        'excess_monitoring_type': mapping_row['excess_monitoring_type'],
                        'monitoring_value': online_row['monitoring_value'],
                        'standard_value': online_row['standard_value'],
                        'missing_exec_record': {
                            'type': 'missing_exec_record',
                            'description': '执行报告中无对应超标记录',
                            'exec_records': None
                        }
                    }
                    anomaly_record['pollutant_issues'].append(pollutant_issue)

            if anomaly_record['pollutant_issues']:
                anomalies.append(anomaly_record)

        logger.info(f'发现{len(anomalies)}条缺失执行报告记录的问题')
        return anomalies

    # 转换时间字段
    online_air_data = online_air_data.copy()
    exec_air_data = exec_air_data.copy()
    online_air_data['monitoring_time'] = pd.to_datetime(online_air_data['monitoring_time'])
    exec_air_data['over_std_start_time'] = pd.to_datetime(exec_air_data['over_std_start_time'])
    exec_air_data['over_std_end_time'] = pd.to_datetime(exec_air_data['over_std_end_time'])

    # 按在线监测记录分组（企业+排口+时间）
    online_groups = online_air_data.groupby(['online_enterprise_id', 'emission_id', 'monitoring_time'])

    for group_key, group_data in online_groups:
        online_enterprise_id, emission_id, monitoring_time = group_key

        # 为这个监测记录创建一个综合的问题记录
        anomaly_record = {
            'data_type': 'air',  # 废气
            'online_enterprise_info': {
                'online_enterprise_id': online_enterprise_id,
                'pollution_source_name': group_data.iloc[0]['pollution_source_name'],
                'emission_id': emission_id,
                'emission_name': group_data.iloc[0]['emission_name']
            },
            'monitoring_time': monitoring_time,
            'report_time': transform2quarter(monitoring_time),
            'pollutant_issues': [],  # 存储各个污染物的问题
            'has_issues': False
        }

        # 遍历该监测记录中的每个污染物
        for _, online_row in group_data.iterrows():
            # 查找对应的映射关系
            mapping_match = mapping_data[
                (mapping_data['online_enterprise_id'] == str(online_row['online_enterprise_id'])) &
                (mapping_data['emission_id'] == str(online_row['emission_id'])) &
                (mapping_data['pollution_name'] == online_row['pollution_name'])
            ]

            if mapping_match.empty:
                logger.warning(f'没有找到映射关系：在线监测企业ID={online_row["online_enterprise_id"]}, '
                               f'排口ID={online_row["emission_id"]}, 污染物名称={online_row["pollution_name"]}')
                continue  # 跳过无映射关系的记录

            mapping_row = mapping_match.iloc[0]

            # 设置执行报告企业信息
            if 'exec_enterprise_info' not in anomaly_record:
                anomaly_record['exec_enterprise_info'] = {
                    'f_enterprise_id': mapping_row['f_enterprise_id'],
                    'f_enterprise_name': mapping_row['f_enterprise_name'],
                    'outlet_id': mapping_row['outlet_id'],
                    'outlet_name': mapping_row['outlet_name']
                }

            # 查找对应的执行报告记录
            exec_match = exec_air_data[
                (exec_air_data['f_enterprise_id'] == mapping_row['f_enterprise_id']) &
                (exec_air_data['outlet_id'] == mapping_row['outlet_id']) &
                (exec_air_data['std_code'] == mapping_row['std_code'])
            ]

            # 检查这个污染物的问题
            pollutant_issue = {
                'pollution_name': online_row['pollution_name'],
                'pollution_code': online_row['pollution_code'],
                'std_code': mapping_row['std_code'],
                'excess_monitoring_type': mapping_row['excess_monitoring_type'],
                'monitoring_value': online_row['monitoring_value'],
                'standard_value': online_row['standard_value'],
                'issues': []  # 存储具体问题
            }

            # 检查是否缺失执行报告记录
            if exec_match.empty:
                pollutant_issue['missing_exec_record'] = {
                    'type': 'missing_exec_record',
                    'description': '执行报告中无对应超标记录'
                }
                anomaly_record['has_issues'] = True
            else:
                # 检查时间匹配
                time_matched = False
                matched_exec_record = None

                for _, exec_row in exec_match.iterrows():
                    if check_time_overlap(
                        online_row['monitoring_time'],
                        exec_row['over_std_start_time'],
                        exec_row['over_std_end_time']
                    ):
                        time_matched = True
                        matched_exec_record = exec_row
                        break

                if not time_matched:
                    pollutant_issue['time_mismatch'] = {
                        'type': 'time_mismatch',
                        'description': '超标时间与执行报告时间段不匹配',
                        'exec_records': exec_match.to_dict('records')
                    }
                    anomaly_record['has_issues'] = True

            # 如果有问题，添加到污染物问题列表
            if pollutant_issue['issues']:
                anomaly_record['pollutant_issues'].append(pollutant_issue)

        # 只有存在问题的记录才添加到结果中
        if anomaly_record['has_issues']:
            anomalies.append(anomaly_record)

    logger.info(f'废气超标记录校验完成，发现{len(anomalies)}个问题')

    return anomalies


def validate_water_exceeding_records(online_water_data: pd.DataFrame, exec_water_data: pd.DataFrame,
                                   mapping_data: pd.DataFrame) -> List[Dict]:
    """
    校验废水超标记录

    Args:
        online_water_data (pd.DataFrame): 在线监测废水超标日均值数据
        exec_water_data (pd.DataFrame): 执行报告废水超标数据
        mapping_data (pd.DataFrame): 企业关联映射数据

    Returns:
        List[Dict]: 问题记录列表，每个元素包含同一条在线监测记录的所有问题
    """
    logger.info('开始校验废水超标记录...')

    anomalies = []

    if online_water_data.empty:
        logger.info('无在线监测废水超标数据')
        return anomalies

    if exec_water_data.empty:
        logger.warning('无执行报告废水超标数据，所有在线监测超标记录都是问题')
        # 按在线监测记录分组处理
        online_groups = online_water_data.groupby(['online_enterprise_id', 'emission_id', 'monitoring_date'])

        for group_key, group_data in online_groups:
            online_enterprise_id, emission_id, monitoring_date = group_key

            # 创建问题记录
            anomaly_record = {
                'data_type': 'water',
                'online_enterprise_info': {
                    'online_enterprise_id': online_enterprise_id,
                    'pollution_source_name': group_data.iloc[0]['pollution_source_name'],
                    'emission_id': emission_id,
                    'emission_name': group_data.iloc[0]['emission_name']
                },
                'monitoring_date': monitoring_date,
                'report_time': transform2quarter(monitoring_date),
                'pollutant_issues': [],
                'has_issues': True
            }

            # 处理每个污染物
            for _, online_row in group_data.iterrows():
                mapping_match = mapping_data[
                    (mapping_data['online_enterprise_id'] == str(online_row['online_enterprise_id'])) &
                    (mapping_data['emission_id'] == str(online_row['emission_id'])) &
                    (mapping_data['pollution_name'] == online_row['pollution_name'])
                ]

                if not mapping_match.empty:
                    mapping_row = mapping_match.iloc[0]

                    # 设置执行报告企业信息
                    if 'exec_enterprise_info' not in anomaly_record:
                        anomaly_record['exec_enterprise_info'] = {
                            'f_enterprise_id': mapping_row['f_enterprise_id'],
                            'f_enterprise_name': mapping_row['f_enterprise_name'],
                            'outlet_id': mapping_row['outlet_id'],
                            'outlet_name': mapping_row['outlet_name']
                        }

                    pollutant_issue = {
                        'pollution_name': online_row['pollution_name'],
                        'pollution_code': online_row['pollution_code'],
                        'std_code': mapping_row['std_code'],
                        'excess_monitoring_type': mapping_row['excess_monitoring_type'],
                        'daily_avg_value': online_row['daily_avg_value'],
                        'standard_value': online_row['standard_value'],
                        'daily_record_count': online_row['daily_record_count'],
                        'missing_exec_record': {
                            'type': 'missing_exec_record',
                            'description': '执行报告中无对应超标记录'
                        }
                    }
                    anomaly_record['pollutant_issues'].append(pollutant_issue)

            if anomaly_record['pollutant_issues']:
                anomalies.append(anomaly_record)

        logger.info(f'发现{len(anomalies)}条缺失执行报告记录的问题')
        return anomalies

    # 转换时间字段
    exec_water_data = exec_water_data.copy()
    exec_water_data['over_std_start_time'] = pd.to_datetime(exec_water_data['over_std_start_time'])
    exec_water_data['over_std_end_time'] = pd.to_datetime(exec_water_data['over_std_end_time'])

    # 按在线监测记录分组（企业+排口+日期）
    online_groups = online_water_data.groupby(['online_enterprise_id', 'emission_id', 'monitoring_date'])

    for group_key, group_data in online_groups:
        online_enterprise_id, emission_id, monitoring_date = group_key

        # 为这个监测记录创建一个综合的问题记录
        anomaly_record = {
            'data_type': 'water',  # 废水
            'online_enterprise_info': {
                'online_enterprise_id': online_enterprise_id,
                'pollution_source_name': group_data.iloc[0]['pollution_source_name'],
                'emission_id': emission_id,
                'emission_name': group_data.iloc[0]['emission_name']
            },
            'monitoring_date': monitoring_date,
            'report_time': transform2quarter(monitoring_date),
            'pollutant_issues': [],  # 存储各个污染物的问题
            'has_issues': False
        }

        # 遍历该监测记录中的每个污染物
        for _, online_row in group_data.iterrows():
            # 查找对应的映射关系
            mapping_match = mapping_data[
                (mapping_data['online_enterprise_id'] == str(online_row['online_enterprise_id'])) &
                (mapping_data['emission_id'] == str(online_row['emission_id'])) &
                (mapping_data['pollution_name'] == online_row['pollution_name'])
            ]

            if mapping_match.empty:
                logger.warning(f'没有找到映射关系：在线监测企业ID={online_row["online_enterprise_id"]}, '
                               f'排口ID={online_row["emission_id"]}, 污染物名称={online_row["pollution_name"]}')
                continue  # 跳过无映射关系的记录

            mapping_row = mapping_match.iloc[0]

            # 设置执行报告企业信息
            if 'exec_enterprise_info' not in anomaly_record:
                anomaly_record['exec_enterprise_info'] = {
                    'f_enterprise_id': mapping_row['f_enterprise_id'],
                    'f_enterprise_name': mapping_row['f_enterprise_name'],
                    'outlet_id': mapping_row['outlet_id'],
                    'outlet_name': mapping_row['outlet_name']
                }

            # 查找对应的执行报告记录
            exec_match = exec_water_data[
                (exec_water_data['f_enterprise_id'] == mapping_row['f_enterprise_id']) &
                (exec_water_data['outlet_id'] == mapping_row['outlet_id']) &
                (exec_water_data['std_code'] == mapping_row['std_code'])
            ]

            # 检查这个污染物的问题
            pollutant_issue = {
                'pollution_name': online_row['pollution_name'],
                'pollution_code': online_row['pollution_code'],
                'std_code': mapping_row['std_code'],
                'excess_monitoring_type': mapping_row['excess_monitoring_type'],
                'daily_avg_value': online_row['daily_avg_value'],
                'standard_value': online_row['standard_value'],
                'daily_record_count': online_row['daily_record_count'],
                'issues': []  # 存储具体问题
            }

            # 检查是否缺失执行报告记录
            if exec_match.empty:
                pollutant_issue['missing_exec_record'] = {
                    'type': 'missing_exec_record',
                    'description': '执行报告中无对应超标记录'
                }
                anomaly_record['has_issues'] = True
            else:
                # 检查时间和浓度匹配
                time_matched = False
                concentration_matched = False
                matched_exec_record = None

                online_date = pd.to_datetime(online_row['monitoring_date'])

                for _, exec_row in exec_match.iterrows():
                    # 检查日期是否在执行报告超标时间段内（只精确到天）
                    exec_start_date = exec_row['over_std_start_time'].date()
                    exec_end_date = exec_row['over_std_end_time'].date()
                    online_date_only = online_date.date()

                    if exec_start_date <= online_date_only <= exec_end_date:
                        time_matched = True
                        matched_exec_record = exec_row

                        # 检查浓度是否在正负10%以内
                        if check_concentration_tolerance(
                            online_row['daily_avg_value'],
                            exec_row['actual_emission_con']
                        ):
                            concentration_matched = True
                            break

                if not time_matched:
                    pollutant_issue['time_mismatch'] = {
                        'type': 'time_mismatch',
                        'description': '超标日期与执行报告时间段不匹配',
                        'exec_records': exec_match.to_dict('records')
                    }
                    anomaly_record['has_issues'] = True
                elif time_matched and not concentration_matched:
                    pollutant_issue['concentration_mismatch'] = {
                        'type': 'concentration_mismatch',
                        'description': f'日均值({online_row["daily_avg_value"]})与执行报告浓度({matched_exec_record["actual_emission_con"]})差异超过10%',
                        'exec_record': {
                            'over_std_start_time': matched_exec_record['over_std_start_time'],
                            'over_std_end_time': matched_exec_record['over_std_end_time'],
                            'actual_emission_con': matched_exec_record['actual_emission_con']
                        }
                    }
                    anomaly_record['has_issues'] = True

            # 如果有问题，添加到污染物问题列表
            if pollutant_issue['issues']:
                anomaly_record['pollutant_issues'].append(pollutant_issue)

        # 只有存在问题的记录才添加到结果中
        if anomaly_record['has_issues']:
            anomalies.append(anomaly_record)

    logger.info(f'废水超标记录校验完成，发现{len(anomalies)}个问题')

    return anomalies


@timing_decorator
def check_exe_online_exceeding(city: str, start_date: str, end_date: str) -> List[Dict]:
    """
    执行报告与在线监测超标数据校验主入口函数

    Args:
        city (str): 城市名称
        start_date (str): 起始日期，格式：'YYYY-MM-DD'
        end_date (str): 结束日期，格式：'YYYY-MM-DD'

    Returns:
        List[Dict]: 存在问题的记录数组，包含废气和废水问题记录。
        数组中每个元素代表一条在线监测记录中发现的所有问题，结构如下：

        废气记录结构：
        {
            'data_type': 'air',                              # 废气数据标识
            'online_enterprise_info': {                      # 在线监测企业信息
                'online_enterprise_id': str,                 # 在线监测企业ID
                'pollution_source_name': str,                # 污染源名称
                'emission_id': str,                          # 排放口ID
                'emission_name': str                         # 排放口名称
            },
            'exec_enterprise_info': {                        # 执行报告企业信息
                'f_enterprise_id': str,                      # 执行报告企业ID
                'f_enterprise_name': str,                    # 执行报告企业名称
                'outlet_id': str,                             # 执行报告排口ID
                'outlet_name': str                            # 执行报告排口名称
            },
            'monitoring_time': datetime,                     # 监测时间（精确到小时）
            'report_time': str,                             # 报告时间（季度）
            'pollutant_issues': [                           # 污染物问题列表
                {
                    'pollution_name': str,                   # 污染物名称（氮氧化物、二氧化硫、非甲烷总烃、颗粒物）
                    'pollution_code': str,                   # 污染物编码
                    'std_code': str,                         # 标准因子编码
                    'over_std_start_time': datetime,           # 超标开始时间
                    'monitoring_value': float,               # 小时监测值
                    'standard_value': float,                 # 标准限值
                    'issues': [                              # 具体问题列表
                        {
                            'type': 'missing_exec_record|time_mismatch',  # 问题类型
                            'description': str,              # 问题描述
                            'exec_records': list             # 相关执行报告记录（可选）
                        }
                    ]
                }
            ],
            'has_issues': bool                               # 是否存在问题
        }

        废水记录结构：
        {
            'data_type': 'water',                            # 废水数据标识
            'online_enterprise_info': {                      # 在线监测企业信息
                'online_enterprise_id': str,                 # 在线监测企业ID
                'pollution_source_name': str,                # 污染源名称
                'emission_id': str,                          # 排放口ID
                'emission_name': str                         # 排放口名称
            },
            'exec_enterprise_info': {                        # 执行报告企业信息
                'f_enterprise_id': str,                      # 执行报告企业ID
                'f_enterprise_name': str,                    # 执行报告企业名称
                'outlet_id': str,                             # 执行报告排口ID
                'outlet_name': str                            # 执行报告排口名称
            },
            'monitoring_date': date,                         # 监测日期（精确到天）
            'report_time': str,                             # 报告时间（季度）
            'pollutant_issues': [                           # 污染物问题列表
                {
                    'pollution_name': str,                   # 污染物名称（总磷、总氮、氨氮、化学需氧量）
                    'pollution_code': str,                   # 污染物编码
                    'std_code': str,                         # 标准因子编码
                    'over_std_start_time': datetime,           # 超标开始时间
                    'daily_avg_value': float,                # 日均值
                    'standard_value': float,                 # 标准限值
                    'daily_record_count': int,               # 当日有效监测记录数
                    'issues': [                              # 具体问题列表
                        {
                            'type': 'missing_exec_record|time_mismatch|concentration_mismatch',  # 问题类型
                            'description': str,              # 问题描述
                            'exec_records': list,            # 相关执行报告记录（时间不匹配时）
                            'exec_record': dict              # 匹配的执行报告记录（浓度不匹配时）
                        }
                    ]
                }
            ],
            'has_issues': bool                               # 是否存在问题
        }

        问题类型说明：
        - missing_exec_record: 执行报告中缺失对应超标记录
        - time_mismatch: 超标时间与执行报告时间段不匹配
        - concentration_mismatch: 日均值与执行报告浓度差异超过10%（仅废水）

        注意事项：
        1. 返回数组中每个元素代表一条在线监测记录（企业+排口+时间）的所有问题
        2. 同一条监测记录可能包含多个污染物的问题
        3. 每个污染物可能有多种类型的问题
        4. 废气和废水的数据结构略有差异（时间字段、监测值字段）
        5. 所有字段都包含详细的中文注释说明
    """
    logger.info(f'开始执行报告与在线监测超标数据校验 - 城市: {city}, 时间范围: {start_date} 至 {end_date}')

    # 记录开始时间
    start_time = time.time()

    # 验证输入参数
    if not validate_input_params(city, start_date, end_date):
        raise ValueError("输入参数验证失败")

    try:
        # 1. 获取城市编码
        try:
            city_code = get_city_code(city)
        except Exception as e:
            logger.error(f'获取城市编码失败: {str(e)}')
            raise ValueError(f'无法获取城市"{city}"的编码: {str(e)}')

        # 2. 获取污染物标准因子编码
        try:
            air_std_codes = get_pollutant_std_codes('a')  # 废气
            water_std_codes = get_pollutant_std_codes('w')  # 废水

            if not air_std_codes and not water_std_codes:
                logger.warning('未获取到任何污染物标准因子编码')
                return []

        except Exception as e:
            logger.error(f'获取污染物标准因子编码失败: {str(e)}')
            raise ValueError(f'无法获取污染物标准因子编码: {str(e)}')

        # 3. 获取企业关联映射数据
        try:
            mapping_data = get_enterprise_mapping(city_code)

            if mapping_data is None or mapping_data.empty:
                logger.warning(f'城市"{city}"没有企业关联映射数据')
                return []

        except Exception as e:
            logger.error(f'获取企业关联映射数据失败: {str(e)}')
            raise ValueError(f'无法获取企业关联映射数据: {str(e)}')

        # 4. 提取在线监测超标数据
        online_air_data = extract_online_air_exceeding_data(city_code, start_date, end_date, air_std_codes)
        online_water_data = extract_online_water_exceeding_data(city_code, start_date, end_date, water_std_codes)

        # 5. 提取执行报告超标数据
        exec_air_data = extract_exec_air_exceeding_data(city_code, start_date, end_date)
        exec_water_data = extract_exec_water_exceeding_data(city_code, start_date, end_date)

        def add_null(data):
            for item1 in data:
                for item2 in item1['polluttant_issues']:
                    if 'missing_exec_record' not in item2:
                        item2['missing_exec_record'] = {}
                    if 'time_mismatch' not in item2:
                        item2['time_mismatch'] = {}
                    if 'concentration_mismatch' not in item2:
                        item2['concentration_mismatch'] = {}
            return data

        # 6. 校验废气超标记录
        air_anomalies = []
        if not online_air_data.empty or not exec_air_data.empty:
            air_anomalies = validate_air_exceeding_records(online_air_data, exec_air_data, mapping_data)
            air_anomalies = add_null(air_anomalies)

        # 7. 校验废水超标记录
        water_anomalies = []
        if not online_water_data.empty or not exec_water_data.empty:
            water_anomalies = validate_water_exceeding_records(online_water_data, exec_water_data, mapping_data)
            water_anomalies = add_null(water_anomalies)

        # 8. 合并结果
        all_anomalies = air_anomalies + water_anomalies

        # 9. 更新性能统计
        end_time = time.time()
        run_time = end_time - start_time

        update_performance_stats(run_time, all_anomalies)

        # 10. 输出统计信息
        logger.info(f'校验完成 - 总问题记录: {len(all_anomalies)} 条 (耗时: {run_time:.2f}秒)')
        logger.info(f'  废气问题: {len(air_anomalies)} 条')
        logger.info(f'  废水问题: {len(water_anomalies)} 条')
        logger.info(f'  数据统计: 在线监测废气{len(online_air_data)}条, 废水{len(online_water_data)}条; '
                    f'执行报告废气{len(exec_air_data)}条, 废水{len(exec_water_data)}条')

        # 按问题类型统计（新格式）
        issue_type_stats = {}
        total_pollutant_issues = 0

        for anomaly in all_anomalies:
            for pollutant_issue in anomaly.get('pollutant_issues', []):
                total_pollutant_issues += 1
                for issue in pollutant_issue.get('issues', []):
                    issue_type = issue.get('type', 'unknown')
                    issue_type_stats[issue_type] = issue_type_stats.get(issue_type, 0) + 1

        logger.info('问题类型统计:')
        logger.info(f'  总污染物问题数: {total_pollutant_issues} 个')
        for issue_type, count in issue_type_stats.items():
            type_name = {
                'missing_exec_record': '缺失执行报告记录',
                'time_mismatch': '时间不匹配',
                'concentration_mismatch': '浓度不匹配'
            }.get(issue_type, issue_type)
            logger.info(f'  {type_name}: {count} 个')

        return all_anomalies

    except Exception as e:
        logger.error(f'执行报告与在线监测超标数据校验过程中发生错误: {str(e)}')
        raise


def update_performance_stats(run_time: float, anomalies: List[Dict]):
    """
    性能统计信息

    Args:
        run_time: 运行时间（秒）
        anomalies: 异常记录列表
    """
    global _PERFORMANCE_STATS

    try:
        _PERFORMANCE_STATS['total_runs'] += 1
        _PERFORMANCE_STATS['last_run_time'] = run_time
        _PERFORMANCE_STATS['total_anomalies'] += len(anomalies)
    except Exception as e:
        logger.warning(f'更新性能统计时发生错误: {str(e)}')


def clear_cache():
    """清空缓存"""
    global _CACHE
    try:
        _CACHE = {
            'city_codes': {},
            'std_codes': {},
            'enterprise_mappings': {},
            'last_update': {}
        }
        logger.info('缓存已清空')
    except Exception as e:
        logger.error(f'清空缓存时发生错误: {str(e)}')


def get_performance_stats() -> Dict:
    """获取性能统计信息"""
    try:
        return _PERFORMANCE_STATS.copy()
    except Exception as e:
        logger.warning(f'获取性能统计时发生错误: {str(e)}')
        return {'total_runs': 0, 'last_run_time': 0.0, 'total_anomalies': 0}


if __name__ == '__main__':
    # 示例调用
    city = '九龙坡区'
    start_date = '2023-01-01'
    end_date = '2026-01-31'

    try:
        result = check_exe_online_exceeding(city, start_date, end_date)
        print(f"发现问题记录: {len(result)} 条")

        # 显示前几条问题记录的详细信息
        if result:
            print("\n=== 问题记录示例 ===")
            for i, anomaly in enumerate(result[:2]):  # 只显示前2条
                print(f"\n问题记录 {i+1}:")
                print(f"  数据类型: {anomaly.get('data_type', 'N/A')}")
                print(f"  监测时间: {anomaly.get('monitoring_time', anomaly.get('monitoring_date', 'N/A'))}")

                if 'exec_enterprise_info' in anomaly:
                    exec_info = anomaly['exec_enterprise_info']
                    print(f"  执行报告企业: {exec_info.get('f_enterprise_name', 'N/A')}")
                    print(f"  排口编号: {exec_info.get('outlet_id', 'N/A')}")

                if 'online_enterprise_info' in anomaly:
                    online_info = anomaly['online_enterprise_info']
                    print(f"  在线监测企业: {online_info.get('pollution_source_name', 'N/A')}")
                    print(f"  排放口ID: {online_info.get('emission_id', 'N/A')}")

                print(f"  污染物问题数: {len(anomaly.get('pollutant_issues', []))}")
                for j, pollutant_issue in enumerate(anomaly.get('pollutant_issues', [])[:2]):  # 只显示前2个污染物
                    print(f"    污染物{j+1}: {pollutant_issue.get('pollution_name', 'N/A')}")
                    print(f"      问题数: {len(pollutant_issue.get('issues', []))}")
                    for issue in pollutant_issue.get('issues', []):
                        print(f"        - {issue.get('type', 'N/A')}: {issue.get('description', 'N/A')}")

    except Exception as e:
        print(f"执行失败: {str(e)}")
        logger.error(f"执行失败: {str(e)}")